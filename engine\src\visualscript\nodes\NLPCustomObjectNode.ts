/**
 * 自定义对象类型注册节点
 */
import { Node, NodeCategory, SocketDirection, SocketType, NodeOptions } from './Node';
import { NLPSceneGenerator } from '../../ai/NLPSceneGenerator';
import * as THREE from 'three';

export class NLPCustomObjectNode extends Node {
  public readonly category: NodeCategory = NodeCategory.AI;

  constructor(options: NodeOptions) {
    super(options);

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '注册自定义对象';
    }
    if (!this.metadata.description) {
      this.metadata.description = '注册自定义3D对象类型';
    }
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'object_name',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '对象名称',
      optional: false
    });

    this.addInput({
      name: 'category',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '对象类别',
      defaultValue: 'custom',
      optional: true
    });

    this.addInput({
      name: 'description',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '对象描述',
      optional: true
    });

    this.addInput({
      name: 'geometry_type',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '几何体类型',
      defaultValue: 'box',
      optional: true
    });

    this.addInput({
      name: 'geometry_params',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '几何体参数',
      optional: true
    });

    this.addInput({
      name: 'default_material',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '默认材质',
      defaultValue: 'standard',
      optional: true
    });

    this.addInput({
      name: 'tags',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'array',
      description: '标签列表',
      optional: true
    });

    this.addInput({
      name: 'complexity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '复杂度 (1-10)',
      defaultValue: 5,
      optional: true
    });

    this.addInput({
      name: 'bounding_box',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '边界框',
      optional: true
    });

    // 输出插槽
    this.addOutput({
      name: 'object_config',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '对象配置'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '注册成功'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '注册失败'
    });
  }

  protected executeImpl(): any {
    const objectName = this.getInputValue('object_name') as string;
    const category = this.getInputValue('category') as string || 'custom';
    const description = this.getInputValue('description') as string || '';
    const geometryType = this.getInputValue('geometry_type') as string || 'box';
    const geometryParams = this.getInputValue('geometry_params') as any || {};
    const defaultMaterial = this.getInputValue('default_material') as string || 'standard';
    const tags = this.getInputValue('tags') as string[] || [];
    const complexity = Math.max(1, Math.min(10, this.getInputValue('complexity') as number || 5));
    const boundingBox = this.getInputValue('bounding_box') as any;

    // 验证输入
    if (!objectName || objectName.trim().length === 0) {
      this.setOutputValue('error', '对象名称不能为空');
      this.triggerFlow('error');
      return { success: false, error: '对象名称不能为空' };
    }

    try {
      // 获取NLP场景生成器
      const nlpGenerator = this.context.world.getSystem(NLPSceneGenerator);

      if (!nlpGenerator) {
        throw new Error('自然语言场景生成器未初始化');
      }

      // 创建几何体工厂函数
      const geometryFactory = (params: any): THREE.BufferGeometry => {
        const mergedParams = { ...geometryParams, ...params };

        switch (geometryType) {
          case 'box':
            return new THREE.BoxGeometry(
              mergedParams.width || 1,
              mergedParams.height || 1,
              mergedParams.depth || 1
            );
          case 'sphere':
            return new THREE.SphereGeometry(
              mergedParams.radius || 0.5,
              mergedParams.widthSegments || 32,
              mergedParams.heightSegments || 16
            );
          case 'cylinder':
            return new THREE.CylinderGeometry(
              mergedParams.radiusTop || mergedParams.radius || 0.5,
              mergedParams.radiusBottom || mergedParams.radius || 0.5,
              mergedParams.height || 1,
              mergedParams.radialSegments || 32
            );
          case 'cone':
            return new THREE.ConeGeometry(
              mergedParams.radius || 0.5,
              mergedParams.height || 1,
              mergedParams.radialSegments || 32
            );
          default:
            return new THREE.BoxGeometry(1, 1, 1);
        }
      };

      // 构建对象配置
      const objectConfig = {
        name: objectName,
        category,
        description,
        geometryFactory,
        defaultMaterial,
        boundingBox: boundingBox || new THREE.Box3(
          new THREE.Vector3(-0.5, 0, -0.5),
          new THREE.Vector3(0.5, 1, 0.5)
        ),
        tags: [...tags, category, 'custom'],
        complexity,
        additionalComponents: []
      };

      // 注册自定义对象类型
      nlpGenerator.registerCustomObject(objectConfig);

      // 设置输出值
      this.setOutputValue('object_config', objectConfig);

      this.triggerFlow('success');
      return { success: true, objectConfig };

    } catch (error: any) {
      console.error('注册自定义对象失败:', error);
      this.setOutputValue('error', error.message);
      this.triggerFlow('error');
      return { success: false, error: error.message };
    }
  }

  // 节点配置
  getConfiguration(): any {
    return {
      color: '#FF5722',
      icon: 'cube',
      category: 'AI',
      tags: ['nlp', 'object', 'custom', 'register'],
      examples: [
        {
          name: '注册自定义桌子',
          description: '注册一个自定义桌子对象',
          inputs: {
            object_name: 'custom_table',
            category: 'furniture',
            description: '自定义桌子',
            geometry_type: 'box',
            geometry_params: { width: 2, height: 0.1, depth: 1 },
            tags: ['furniture', 'table'],
            complexity: 3
          }
        },
        {
          name: '注册装饰球',
          description: '注册一个装饰球对象',
          inputs: {
            object_name: 'decoration_sphere',
            category: 'decoration',
            description: '装饰球体',
            geometry_type: 'sphere',
            geometry_params: { radius: 0.3 },
            tags: ['decoration', 'sphere'],
            complexity: 2
          }
        }
      ]
    };
  }
}
