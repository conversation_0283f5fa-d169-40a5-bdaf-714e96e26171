/**
 * 自定义风格注册节点
 */
import { Node, NodeCategory, SocketType } from './Node';

export class NLPCustomStyleNode extends Node {
  constructor(options: any) {
    super({
      ...options,
      type: 'nlp/style/register',
      category: NodeCategory.AI,
      name: '注册自定义风格',
      description: '注册自定义场景生成风格'
    });

    this.setupSockets();
  }

  private setupSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'style_name',
      type: SocketType.DATA,
      dataType: 'string',
      description: '风格名称',
      optional: false
    });

    this.addInput({
      name: 'description',
      type: SocketType.DATA,
      dataType: 'string',
      description: '风格描述',
      optional: true
    });

    this.addInput({
      name: 'material_presets',
      type: SocketType.DATA,
      dataType: 'array',
      description: '材质预设列表',
      optional: true
    });

    this.addInput({
      name: 'lighting_presets',
      type: SocketType.DATA,
      dataType: 'array',
      description: '光照预设列表',
      optional: true
    });

    this.addInput({
      name: 'color_palette',
      type: SocketType.DATA,
      dataType: 'array',
      description: '颜色调色板',
      optional: true
    });

    this.addInput({
      name: 'atmosphere_settings',
      type: SocketType.DATA,
      dataType: 'object',
      description: '氛围设置',
      optional: true
    });

    // 输出插槽
    this.addOutput({
      name: 'style_config',
      type: SocketType.DATA,
      dataType: 'object',
      description: '风格配置对象'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      description: '注册成功'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.FLOW,
      description: '注册失败'
    });
  }

  protected executeImpl(): any {
    const styleName = this.getInputValue('style_name') as string;
    const description = this.getInputValue('description') as string || '';
    const materialPresets = this.getInputValue('material_presets') as any[] || [];
    const lightingPresets = this.getInputValue('lighting_presets') as any[] || [];
    const colorPalette = this.getInputValue('color_palette') as string[] || ['#ffffff', '#000000'];
    const atmosphereSettings = this.getInputValue('atmosphere_settings') as any || {};

    // 验证输入
    if (!styleName || styleName.trim().length === 0) {
      this.setOutputValue('error', '风格名称不能为空');
      this.triggerFlow('error');
      return { success: false, error: '风格名称不能为空' };
    }

    try {
      const world = this.context.world;
      const nlpGenerator = world.getSystem('NLPSceneGenerator');

      if (!nlpGenerator) {
        throw new Error('自然语言场景生成器未初始化');
      }

      // 构建风格配置
      const styleConfig = {
        name: styleName,
        description,
        materialPresets,
        lightingPresets,
        objectModifiers: [],
        atmosphereSettings: {
          fogDensity: 0.0,
          fogColor: '#ffffff',
          skyboxType: 'default',
          postProcessingEffects: [],
          ...atmosphereSettings
        },
        colorPalette
      };

      // 注册自定义风格
      (nlpGenerator as any).registerCustomStyle(styleConfig);

      // 设置输出值
      this.setOutputValue('style_config', styleConfig);

      this.triggerFlow('success');
      return { success: true, styleConfig };

    } catch (error: any) {
      console.error('注册自定义风格失败:', error);
      this.setOutputValue('error', error.message);
      this.triggerFlow('error');
      return { success: false, error: error.message };
    }
  }

  // 节点配置
  getConfiguration(): any {
    return {
      color: '#9C27B0',
      icon: 'palette',
      category: 'AI',
      tags: ['nlp', 'style', 'custom', 'register'],
      examples: [
        {
          name: '注册现代风格',
          description: '注册一个现代简约风格',
          inputs: {
            style_name: 'modern_minimal',
            description: '现代简约风格',
            color_palette: ['#ffffff', '#f5f5f5', '#e0e0e0', '#bdbdbd']
          }
        },
        {
          name: '注册工业风格',
          description: '注册一个工业风格',
          inputs: {
            style_name: 'industrial',
            description: '工业风格',
            color_palette: ['#424242', '#616161', '#757575', '#9e9e9e']
          }
        }
      ]
    };
  }
}
