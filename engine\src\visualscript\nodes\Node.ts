/**
 * 视觉脚本节点基类
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { ExecutionContext } from '../execution/ExecutionContext';
import { Graph } from '../graph/Graph';
import { NodeMetadata } from '../graph/GraphJSON';

/**
 * 节点插槽类型
 */
export enum SocketType {
  /** 流程插槽 */
  FLOW = 'flow',
  /** 数据插槽 */
  DATA = 'data'
}

/**
 * 节点插槽方向
 */
export enum SocketDirection {
  /** 输入插槽 */
  INPUT = 'input',
  /** 输出插槽 */
  OUTPUT = 'output'
}

/**
 * 节点插槽定义
 */
export interface SocketDefinition {
  /** 插槽名称 */
  name: string;
  /** 插槽类型 */
  type: SocketType;
  /** 插槽方向 */
  direction: SocketDirection;
  /** 数据类型（对于数据插槽） */
  dataType?: string;
  /** 默认值（对于数据插槽） */
  defaultValue?: any;
  /** 插槽描述 */
  description?: string;
  /** 是否可选 */
  optional?: boolean;
}

/**
 * 节点插槽创建定义（不需要direction）
 */
export interface SocketCreateDefinition {
  /** 插槽名称 */
  name: string;
  /** 插槽类型 */
  type: SocketType;
  /** 数据类型（对于数据插槽） */
  dataType?: string;
  /** 默认值（对于数据插槽） */
  defaultValue?: any;
  /** 插槽描述 */
  description?: string;
  /** 是否可选 */
  optional?: boolean;
}

/**
 * 节点插槽
 */
export interface Socket extends SocketDefinition {
  /** 连接的节点ID */
  connectedNodeId?: string;
  /** 连接的插槽名称 */
  connectedSocketName?: string;
  /** 当前值 */
  value?: any;
}

/**
 * 节点连接
 */
export interface NodeConnection {
  /** 源节点 */
  sourceNode: Node;
  /** 源插槽名称 */
  sourceSocketName: string;
  /** 目标节点 */
  targetNode: Node;
  /** 目标插槽名称 */
  targetSocketName: string;
}

/**
 * 节点类型
 */
export enum NodeType {
  /** 普通节点 */
  NORMAL = 'normal',
  /** 事件节点 */
  EVENT = 'event',
  /** 函数节点 */
  FUNCTION = 'function',
  /** 异步节点 */
  ASYNC = 'async'
}

/**
 * 节点类别
 */
export enum NodeCategory {
  /** 流程控制 */
  FLOW = 'flow',
  /** 数学运算 */
  MATH = 'math',
  /** 逻辑运算 */
  LOGIC = 'logic',
  /** 字符串操作 */
  STRING = 'string',
  /** 数组操作 */
  ARRAY = 'array',
  /** 对象操作 */
  OBJECT = 'object',
  /** 变量操作 */
  VARIABLE = 'variable',
  /** 函数操作 */
  FUNCTION = 'function',
  /** 事件操作 */
  EVENT = 'event',
  /** 实体操作 */
  ENTITY = 'entity',
  /** 组件操作 */
  COMPONENT = 'component',
  /** 物理操作 */
  PHYSICS = 'physics',
  /** 动画操作 */
  ANIMATION = 'animation',
  /** 输入操作 */
  INPUT = 'input',
  /** 音频操作 */
  AUDIO = 'audio',
  /** 网络操作 */
  NETWORK = 'network',
  /** AI操作 */
  AI = 'ai',
  /** 调试操作 */
  DEBUG = 'debug',
  /** UI操作 */
  UI = 'ui',
  /** 文件操作 */
  FILE = 'file',
  /** 图像处理 */
  IMAGE = 'image',
  /** 渲染操作 */
  RENDERING = 'rendering',
  /** 工业自动化 */
  INDUSTRIAL = 'industrial',
  /** 空间信息系统 */
  SPATIAL = 'spatial',
  /** 自定义操作 */
  CUSTOM = 'custom'
}

/**
 * 节点选项
 */
export interface NodeOptions {
  /** 节点ID */
  id: string;
  /** 节点类型名称 */
  type: string;
  /** 节点元数据 */
  metadata?: NodeMetadata;
  /** 所属图形 */
  graph: Graph;
  /** 执行上下文 */
  context: ExecutionContext;
}

/**
 * 节点执行状态
 */
export enum NodeExecutionState {
  /** 未执行 */
  NOT_EXECUTED = 'not_executed',
  /** 执行中 */
  EXECUTING = 'executing',
  /** 已执行 */
  EXECUTED = 'executed',
  /** 执行失败 */
  FAILED = 'failed',
  /** 已暂停 */
  PAUSED = 'paused'
}

/**
 * 节点执行结果
 */
export interface NodeExecutionResult {
  /** 是否成功 */
  success: boolean;
  /** 执行结果值 */
  value?: any;
  /** 错误信息 */
  error?: Error;
  /** 执行时间（毫秒） */
  executionTime: number;
  /** 输出值映射 */
  outputs?: Map<string, any>;
}

/**
 * 节点验证结果
 */
export interface NodeValidationResult {
  /** 是否有效 */
  valid: boolean;
  /** 错误信息列表 */
  errors: string[];
  /** 警告信息列表 */
  warnings: string[];
}

/**
 * 节点性能统计
 */
export interface NodePerformanceStats {
  /** 总执行次数 */
  executionCount: number;
  /** 总执行时间（毫秒） */
  totalExecutionTime: number;
  /** 平均执行时间（毫秒） */
  averageExecutionTime: number;
  /** 最小执行时间（毫秒） */
  minExecutionTime: number;
  /** 最大执行时间（毫秒） */
  maxExecutionTime: number;
  /** 失败次数 */
  failureCount: number;
}

/**
 * 节点基类
 */
export class Node extends EventEmitter {
  /** 节点ID */
  public readonly id: string;

  /** 节点类型名称 */
  public readonly type: string;

  /** 节点类型 */
  public readonly nodeType: NodeType = NodeType.NORMAL;

  /** 节点类别 */
  public readonly category: NodeCategory = NodeCategory.CUSTOM;

  /** 节点元数据 */
  public metadata: NodeMetadata;

  /** 所属图形 */
  protected graph: Graph;

  /** 执行上下文 */
  protected context: ExecutionContext;

  /** 输入插槽 */
  protected inputs: Map<string, Socket> = new Map();

  /** 输出插槽 */
  protected outputs: Map<string, Socket> = new Map();

  /** 输入连接 */
  protected inputConnections: Map<string, NodeConnection> = new Map();

  /** 输出连接 */
  protected outputConnections: Map<string, NodeConnection[]> = new Map();

  /** 执行状态 */
  protected executionState: NodeExecutionState = NodeExecutionState.NOT_EXECUTED;

  /** 最后执行结果 */
  protected lastExecutionResult: NodeExecutionResult | null = null;

  /** 性能统计 */
  protected performanceStats: NodePerformanceStats = {
    executionCount: 0,
    totalExecutionTime: 0,
    averageExecutionTime: 0,
    minExecutionTime: Infinity,
    maxExecutionTime: 0,
    failureCount: 0
  };

  /** 是否启用调试 */
  protected debugEnabled: boolean = false;

  /** 断点设置 */
  protected breakpointEnabled: boolean = false;

  /** 创建时间 */
  protected createdAt: Date = new Date();

  /** 最后修改时间 */
  protected updatedAt: Date = new Date();

  /**
   * 创建节点
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super();

    this.id = options.id;
    this.type = options.type;
    this.metadata = options.metadata || { positionX: 0, positionY: 0 };
    this.graph = options.graph;
    this.context = options.context;

    // 初始化插槽
    this.initializeSockets();
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 子类实现
  }

  /**
   * 添加输入插槽
   * @param definition 插槽定义
   * @returns 添加的插槽
   */
  protected addInput(definition: SocketCreateDefinition): Socket {
    const socket: Socket = {
      ...definition,
      direction: SocketDirection.INPUT,
      value: definition.defaultValue
    };

    this.inputs.set(socket.name, socket);
    return socket;
  }

  /**
   * 添加输出插槽
   * @param definition 插槽定义
   * @returns 添加的插槽
   */
  protected addOutput(definition: SocketCreateDefinition): Socket {
    const socket: Socket = {
      ...definition,
      direction: SocketDirection.OUTPUT,
      value: definition.defaultValue
    };

    this.outputs.set(socket.name, socket);

    // 初始化输出连接数组
    this.outputConnections.set(socket.name, []);

    return socket;
  }

  /**
   * 获取输入插槽
   * @param name 插槽名称
   * @returns 插槽
   */
  public getInput(name: string): Socket | undefined {
    return this.inputs.get(name);
  }

  /**
   * 获取输出插槽
   * @param name 插槽名称
   * @returns 插槽
   */
  public getOutput(name: string): Socket | undefined {
    return this.outputs.get(name);
  }

  /**
   * 获取所有输入插槽
   * @returns 输入插槽映射
   */
  public getInputs(): Map<string, Socket> {
    return this.inputs;
  }

  /**
   * 获取所有输出插槽
   * @returns 输出插槽映射
   */
  public getOutputs(): Map<string, Socket> {
    return this.outputs;
  }

  /**
   * 获取输入插槽数组
   * @returns 输入插槽数组
   */
  public getInputSockets(): Socket[] {
    return Array.from(this.inputs.values());
  }

  /**
   * 获取输出插槽数组
   * @returns 输出插槽数组
   */
  public getOutputSockets(): Socket[] {
    return Array.from(this.outputs.values());
  }

  /**
   * 获取节点元数据
   * @returns 节点元数据
   */
  public getMetadata(): NodeMetadata {
    return this.metadata;
  }

  /**
   * 设置节点元数据
   * @param metadata 节点元数据
   */
  public setMetadata(metadata: Partial<NodeMetadata>): void {
    this.metadata = { ...this.metadata, ...metadata };
    this.updatedAt = new Date();
    this.emit('metadataChanged', this.metadata);
  }

  /**
   * 获取节点位置
   * @returns 节点位置
   */
  public getPosition(): { x: number; y: number } {
    return {
      x: this.metadata.positionX || 0,
      y: this.metadata.positionY || 0
    };
  }

  /**
   * 设置节点位置
   * @param x X坐标
   * @param y Y坐标
   */
  public setPosition(x: number, y: number): void {
    this.metadata.positionX = x;
    this.metadata.positionY = y;
    this.updatedAt = new Date();
    this.emit('positionChanged', { x, y });
  }

  /**
   * 检查节点是否已执行
   * @returns 是否已执行
   */
  public isExecuted(): boolean {
    return this.executionState === NodeExecutionState.EXECUTED;
  }

  /**
   * 获取执行状态
   * @returns 执行状态
   */
  public getExecutionState(): NodeExecutionState {
    return this.executionState;
  }

  /**
   * 设置执行状态
   * @param state 执行状态
   */
  protected setExecutionState(state: NodeExecutionState): void {
    const oldState = this.executionState;
    this.executionState = state;
    this.emit('executionStateChanged', state, oldState);
  }

  /**
   * 获取最后执行结果
   * @returns 最后执行结果
   */
  public getLastExecutionResult(): NodeExecutionResult | null {
    return this.lastExecutionResult;
  }

  /**
   * 获取性能统计
   * @returns 性能统计
   */
  public getPerformanceStats(): NodePerformanceStats {
    return { ...this.performanceStats };
  }

  /**
   * 重置性能统计
   */
  public resetPerformanceStats(): void {
    this.performanceStats = {
      executionCount: 0,
      totalExecutionTime: 0,
      averageExecutionTime: 0,
      minExecutionTime: Infinity,
      maxExecutionTime: 0,
      failureCount: 0
    };
    this.emit('performanceStatsReset');
  }

  /**
   * 验证节点配置
   * @returns 验证结果
   */
  public validate(): NodeValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 验证必需的输入插槽
    for (const [name, socket] of this.inputs.entries()) {
      if (!socket.optional && socket.value === undefined && !socket.connectedNodeId) {
        errors.push(`必需的输入插槽 "${name}" 没有值或连接`);
      }

      // 验证数据类型
      if (socket.type === SocketType.DATA && socket.connectedNodeId) {
        const sourceNode = this.graph.getNode(socket.connectedNodeId);
        if (sourceNode) {
          const sourceOutput = sourceNode.getOutput(socket.connectedSocketName!);
          if (sourceOutput && sourceOutput.dataType !== socket.dataType) {
            warnings.push(`输入插槽 "${name}" 的数据类型不匹配`);
          }
        }
      }
    }

    // 验证输出插槽
    for (const [name, socket] of this.outputs.entries()) {
      if (socket.type === SocketType.DATA && socket.dataType === undefined) {
        warnings.push(`输出插槽 "${name}" 没有定义数据类型`);
      }
    }

    // 验证循环引用
    if (this.hasCircularReference()) {
      errors.push('检测到循环引用');
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 检查是否存在循环引用
   * @returns 是否存在循环引用
   */
  private hasCircularReference(): boolean {
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const dfs = (nodeId: string): boolean => {
      if (recursionStack.has(nodeId)) {
        return true; // 发现循环
      }

      if (visited.has(nodeId)) {
        return false; // 已访问过，无循环
      }

      visited.add(nodeId);
      recursionStack.add(nodeId);

      const node = this.graph.getNode(nodeId);
      if (node) {
        for (const [, connections] of node.outputConnections.entries()) {
          for (const connection of connections) {
            if (dfs(connection.targetNode.id)) {
              return true;
            }
          }
        }
      }

      recursionStack.delete(nodeId);
      return false;
    };

    return dfs(this.id);
  }

  /**
   * 启用调试模式
   */
  public enableDebug(): void {
    this.debugEnabled = true;
    this.emit('debugEnabled');
  }

  /**
   * 禁用调试模式
   */
  public disableDebug(): void {
    this.debugEnabled = false;
    this.emit('debugDisabled');
  }

  /**
   * 是否启用调试
   * @returns 是否启用调试
   */
  public isDebugEnabled(): boolean {
    return this.debugEnabled;
  }

  /**
   * 设置断点
   */
  public setBreakpoint(): void {
    this.breakpointEnabled = true;
    this.emit('breakpointSet');
  }

  /**
   * 清除断点
   */
  public clearBreakpoint(): void {
    this.breakpointEnabled = false;
    this.emit('breakpointCleared');
  }

  /**
   * 是否设置了断点
   * @returns 是否设置了断点
   */
  public hasBreakpoint(): boolean {
    return this.breakpointEnabled;
  }

  /**
   * 输出调试信息
   * @param message 调试信息
   * @param data 附加数据
   */
  protected debug(message: string, data?: any): void {
    if (this.debugEnabled) {
      console.log(`[Node ${this.id}] ${message}`, data || '');
      this.emit('debug', message, data);
    }
  }

  /**
   * 设置参数值
   * @param name 参数名称
   * @param value 参数值
   */
  public setParameterValue(name: string, value: any): void {
    const input = this.inputs.get(name);

    if (input) {
      input.value = value;
    }
  }

  /**
   * 获取参数值
   * @param name 参数名称
   * @returns 参数值
   */
  public getParameterValue(name: string): any {
    const input = this.inputs.get(name);

    if (input) {
      return input.value;
    }

    return undefined;
  }

  /**
   * 连接输入
   * @param inputName 输入插槽名称
   * @param sourceNode 源节点
   * @param sourceOutputName 源输出插槽名称
   * @returns 是否连接成功
   */
  public connectInput(inputName: string, sourceNode: Node, sourceOutputName: string): boolean {
    const input = this.inputs.get(inputName);
    const sourceOutput = sourceNode.getOutput(sourceOutputName);

    if (!input || !sourceOutput) {
      return false;
    }

    // 检查类型兼容性
    if (input.type !== sourceOutput.type) {
      return false;
    }

    // 如果是数据插槽，检查数据类型兼容性
    if (input.type === SocketType.DATA &&
        input.dataType !== sourceOutput.dataType) {
      return false;
    }

    // 创建连接
    const connection: NodeConnection = {
      sourceNode: sourceNode,
      sourceSocketName: sourceOutputName,
      targetNode: this,
      targetSocketName: inputName
    };

    // 存储连接
    this.inputConnections.set(inputName, connection);

    // 更新插槽连接信息
    input.connectedNodeId = sourceNode.id;
    input.connectedSocketName = sourceOutputName;

    // 添加到源节点的输出连接
    const sourceOutputConnections = sourceNode.outputConnections.get(sourceOutputName) || [];
    sourceOutputConnections.push(connection);
    sourceNode.outputConnections.set(sourceOutputName, sourceOutputConnections);

    // 触发连接事件
    this.emit('inputConnected', inputName, sourceNode, sourceOutputName);
    sourceNode.emit('outputConnected', sourceOutputName, this, inputName);

    return true;
  }

  /**
   * 连接流程
   * @param outputName 输出插槽名称
   * @param targetNode 目标节点
   * @param targetInputName 目标输入插槽名称
   * @returns 是否连接成功
   */
  public connectFlow(outputName: string, targetNode: Node, targetInputName: string): boolean {
    const output = this.outputs.get(outputName);
    const targetInput = targetNode.getInput(targetInputName);

    if (!output || !targetInput) {
      return false;
    }

    // 检查是否为流程插槽
    if (output.type !== SocketType.FLOW || targetInput.type !== SocketType.FLOW) {
      return false;
    }

    // 创建连接
    const connection: NodeConnection = {
      sourceNode: this,
      sourceSocketName: outputName,
      targetNode: targetNode,
      targetSocketName: targetInputName
    };

    // 存储连接
    const outputConnections = this.outputConnections.get(outputName) || [];
    outputConnections.push(connection);
    this.outputConnections.set(outputName, outputConnections);

    // 更新目标插槽连接信息
    targetInput.connectedNodeId = this.id;
    targetInput.connectedSocketName = outputName;

    // 存储到目标节点的输入连接
    targetNode.inputConnections.set(targetInputName, connection);

    // 触发连接事件
    this.emit('outputConnected', outputName, targetNode, targetInputName);
    targetNode.emit('inputConnected', targetInputName, this, outputName);

    return true;
  }

  /**
   * 断开输入连接
   * @param inputName 输入插槽名称
   * @returns 是否断开成功
   */
  public disconnectInput(inputName: string): boolean {
    const connection = this.inputConnections.get(inputName);

    if (!connection) {
      return false;
    }

    // 获取源节点和源插槽
    const sourceNode = connection.sourceNode;
    const sourceSocketName = connection.sourceSocketName;

    // 从源节点的输出连接中移除
    const sourceOutputConnections = sourceNode.outputConnections.get(sourceSocketName) || [];
    const index = sourceOutputConnections.findIndex(conn =>
      conn.targetNode.id === this.id && conn.targetSocketName === inputName);

    if (index !== -1) {
      sourceOutputConnections.splice(index, 1);
      sourceNode.outputConnections.set(sourceSocketName, sourceOutputConnections);
    }

    // 更新插槽连接信息
    const input = this.inputs.get(inputName);
    if (input) {
      input.connectedNodeId = undefined;
      input.connectedSocketName = undefined;
    }

    // 移除连接
    this.inputConnections.delete(inputName);

    // 触发断开事件
    this.emit('inputDisconnected', inputName, sourceNode, sourceSocketName);
    sourceNode.emit('outputDisconnected', sourceSocketName, this, inputName);

    return true;
  }

  /**
   * 断开输出连接
   * @param outputName 输出插槽名称
   * @param targetNode 目标节点
   * @param targetInputName 目标输入插槽名称
   * @returns 是否断开成功
   */
  public disconnectOutput(outputName: string, targetNode: Node, targetInputName: string): boolean {
    const outputConnections = this.outputConnections.get(outputName) || [];

    // 查找连接
    const index = outputConnections.findIndex(conn =>
      conn.targetNode.id === targetNode.id && conn.targetSocketName === targetInputName);

    if (index === -1) {
      return false;
    }

    // 从输出连接中移除
    outputConnections.splice(index, 1);
    this.outputConnections.set(outputName, outputConnections);

    // 从目标节点的输入连接中移除
    targetNode.inputConnections.delete(targetInputName);

    // 更新目标插槽连接信息
    const targetInput = targetNode.getInput(targetInputName);
    if (targetInput) {
      targetInput.connectedNodeId = undefined;
      targetInput.connectedSocketName = undefined;
    }

    // 触发断开事件
    this.emit('outputDisconnected', outputName, targetNode, targetInputName);
    targetNode.emit('inputDisconnected', targetInputName, this, outputName);

    return true;
  }

  /**
   * 断开所有连接
   */
  public disconnectAll(): void {
    // 断开所有输入连接
    for (const inputName of this.inputConnections.keys()) {
      this.disconnectInput(inputName);
    }

    // 断开所有输出连接
    for (const [outputName, connections] of this.outputConnections.entries()) {
      // 复制连接数组，因为在断开过程中会修改原数组
      const connectionsToDisconnect = [...connections];

      for (const connection of connectionsToDisconnect) {
        this.disconnectOutput(outputName, connection.targetNode, connection.targetSocketName);
      }
    }
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 检查断点
    if (this.breakpointEnabled) {
      this.debug('断点触发，暂停执行');
      this.setExecutionState(NodeExecutionState.PAUSED);
      this.emit('breakpointHit');
      return null;
    }

    const startTime = performance.now();
    this.setExecutionState(NodeExecutionState.EXECUTING);
    this.debug('开始执行节点');

    try {
      // 验证节点
      const validation = this.validate();
      if (!validation.valid) {
        throw new Error(`节点验证失败: ${validation.errors.join(', ')}`);
      }

      // 执行具体逻辑（子类实现）
      const result = this.executeImpl();

      // 记录执行结果
      const executionTime = performance.now() - startTime;
      this.recordExecution(true, result, executionTime);

      this.setExecutionState(NodeExecutionState.EXECUTED);
      this.debug('节点执行完成', { result, executionTime });

      return result;
    } catch (error) {
      const executionTime = performance.now() - startTime;
      this.recordExecution(false, error, executionTime);

      this.setExecutionState(NodeExecutionState.FAILED);
      this.debug('节点执行失败', error);

      throw error;
    }
  }

  /**
   * 执行节点的具体实现（子类重写）
   * @returns 执行结果
   */
  protected executeImpl(): any {
    // 子类实现
    return null;
  }

  /**
   * 异步执行节点
   * @returns 执行结果Promise
   */
  public async executeAsync(): Promise<any> {
    return new Promise((resolve, reject) => {
      try {
        const result = this.execute();

        // 如果结果是Promise，等待它完成
        if (result instanceof Promise) {
          result.then(resolve).catch(reject);
        } else {
          resolve(result);
        }
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 记录执行统计
   * @param success 是否成功
   * @param result 执行结果或错误
   * @param executionTime 执行时间
   */
  private recordExecution(success: boolean, result: any, executionTime: number): void {
    this.performanceStats.executionCount++;
    this.performanceStats.totalExecutionTime += executionTime;
    this.performanceStats.averageExecutionTime =
      this.performanceStats.totalExecutionTime / this.performanceStats.executionCount;

    if (executionTime < this.performanceStats.minExecutionTime) {
      this.performanceStats.minExecutionTime = executionTime;
    }

    if (executionTime > this.performanceStats.maxExecutionTime) {
      this.performanceStats.maxExecutionTime = executionTime;
    }

    if (!success) {
      this.performanceStats.failureCount++;
    }

    // 创建执行结果
    this.lastExecutionResult = {
      success,
      value: success ? result : undefined,
      error: success ? undefined : (result instanceof Error ? result : new Error(String(result))),
      executionTime,
      outputs: success ? this.getOutputValues() : undefined
    };

    this.emit('executionRecorded', this.lastExecutionResult);
  }

  /**
   * 获取所有输出值
   * @returns 输出值映射
   */
  private getOutputValues(): Map<string, any> {
    const outputs = new Map<string, any>();
    for (const [name, socket] of this.outputs.entries()) {
      outputs.set(name, socket.value);
    }
    return outputs;
  }

  /**
   * 获取输入值
   * @param name 输入名称
   * @returns 输入值
   */
  public getInputValue(name: string): any {
    const input = this.inputs.get(name);

    if (!input) {
      return undefined;
    }

    // 如果有连接，从源节点获取值
    if (input.connectedNodeId && input.connectedSocketName) {
      const sourceNode = this.graph.getNode(input.connectedNodeId);

      if (sourceNode) {
        const sourceOutput = sourceNode.getOutput(input.connectedSocketName);

        if (sourceOutput) {
          // 如果源节点是函数节点，需要执行它
          if (sourceNode.nodeType === NodeType.FUNCTION) {
            sourceNode.execute();
          }

          return sourceOutput.value;
        }
      }
    }

    // 否则返回当前值
    return input.value;
  }

  /**
   * 设置输出值
   * @param name 输出名称
   * @param value 输出值
   */
  public setOutputValue(name: string, value: any): void {
    const output = this.outputs.get(name);

    if (output) {
      output.value = value;
    }
  }

  /**
   * 触发流程输出
   * @param name 输出名称
   */
  public triggerFlow(name: string): void {
    const connections = this.outputConnections.get(name) || [];

    this.debug(`触发流程输出: ${name}，连接数: ${connections.length}`);

    for (const connection of connections) {
      try {
        // 执行目标节点
        const targetNode = connection.targetNode;
        this.debug(`执行目标节点: ${targetNode.id}`);

        // 如果目标节点是异步节点，使用异步执行
        if (targetNode.nodeType === NodeType.ASYNC) {
          targetNode.executeAsync().catch(error => {
            this.debug(`异步节点执行失败: ${error.message}`, error);
            this.emit('executionError', error, targetNode);
          });
        } else {
          targetNode.execute();
        }
      } catch (error) {
        this.debug(`流程执行失败: ${error}`, error);
        this.emit('flowExecutionError', error, connection);
      }
    }

    this.emit('flowTriggered', name, connections.length);
  }

  /**
   * 序列化节点状态
   * @returns 序列化数据
   */
  public serialize(): any {
    const inputValues: Record<string, any> = {};
    for (const [name, socket] of this.inputs.entries()) {
      inputValues[name] = socket.value;
    }

    const outputValues: Record<string, any> = {};
    for (const [name, socket] of this.outputs.entries()) {
      outputValues[name] = socket.value;
    }

    return {
      id: this.id,
      type: this.type,
      nodeType: this.nodeType,
      category: this.category,
      metadata: this.metadata,
      inputValues,
      outputValues,
      executionState: this.executionState,
      performanceStats: this.performanceStats,
      debugEnabled: this.debugEnabled,
      breakpointEnabled: this.breakpointEnabled,
      createdAt: this.createdAt.toISOString(),
      updatedAt: this.updatedAt.toISOString()
    };
  }

  /**
   * 反序列化节点状态
   * @param data 序列化数据
   */
  public deserialize(data: any): void {
    if (data.metadata) {
      this.metadata = { ...this.metadata, ...data.metadata };
    }

    if (data.inputValues) {
      for (const [name, value] of Object.entries(data.inputValues)) {
        this.setParameterValue(name, value);
      }
    }

    if (data.outputValues) {
      for (const [name, value] of Object.entries(data.outputValues)) {
        this.setOutputValue(name, value);
      }
    }

    if (data.executionState) {
      this.executionState = data.executionState;
    }

    if (data.performanceStats) {
      this.performanceStats = { ...this.performanceStats, ...data.performanceStats };
    }

    if (data.debugEnabled !== undefined) {
      this.debugEnabled = data.debugEnabled;
    }

    if (data.breakpointEnabled !== undefined) {
      this.breakpointEnabled = data.breakpointEnabled;
    }

    if (data.createdAt) {
      this.createdAt = new Date(data.createdAt);
    }

    if (data.updatedAt) {
      this.updatedAt = new Date(data.updatedAt);
    }

    this.emit('deserialized', data);
  }

  /**
   * 克隆节点
   * @param newId 新节点ID
   * @returns 克隆的节点
   */
  public clone(newId: string): Node {
    const serializedData = this.serialize();
    serializedData.id = newId;

    // 创建新节点实例
    const clonedNode = new (this.constructor as any)({
      id: newId,
      type: this.type,
      metadata: { ...this.metadata },
      graph: this.graph,
      context: this.context
    });

    // 反序列化状态
    clonedNode.deserialize(serializedData);

    return clonedNode;
  }

  /**
   * 重置节点状态
   */
  public reset(): void {
    // 重置执行状态
    this.setExecutionState(NodeExecutionState.NOT_EXECUTED);

    // 清空输出值
    for (const [, socket] of this.outputs.entries()) {
      socket.value = socket.defaultValue;
    }

    // 重置输入值为默认值
    for (const [, socket] of this.inputs.entries()) {
      if (!socket.connectedNodeId) {
        socket.value = socket.defaultValue;
      }
    }

    // 清空执行结果
    this.lastExecutionResult = null;

    this.debug('节点状态已重置');
    this.emit('reset');
  }

  /**
   * 初始化节点
   */
  public initialize(): void {
    this.debug('初始化节点');
    this.setExecutionState(NodeExecutionState.NOT_EXECUTED);
    this.emit('initialized');

    // 子类可以重写此方法进行特定初始化
    this.initializeImpl();
  }

  /**
   * 初始化节点的具体实现（子类重写）
   */
  protected initializeImpl(): void {
    // 子类实现
  }

  /**
   * 销毁节点
   */
  public dispose(): void {
    this.debug('销毁节点');

    // 断开所有连接
    this.disconnectAll();

    // 清空插槽
    this.inputs.clear();
    this.outputs.clear();

    // 清空连接
    this.inputConnections.clear();
    this.outputConnections.clear();

    // 重置状态
    this.setExecutionState(NodeExecutionState.NOT_EXECUTED);
    this.lastExecutionResult = null;
    this.resetPerformanceStats();

    // 移除所有事件监听
    this.removeAllListeners();

    this.emit('disposed');
  }

  /**
   * 获取节点信息
   * @returns 节点信息
   */
  public getInfo(): any {
    return {
      id: this.id,
      type: this.type,
      nodeType: this.nodeType,
      category: this.category,
      position: this.getPosition(),
      executionState: this.executionState,
      inputCount: this.inputs.size,
      outputCount: this.outputs.size,
      connectionCount: this.inputConnections.size +
        Array.from(this.outputConnections.values()).reduce((sum, conns) => sum + conns.length, 0),
      performanceStats: this.getPerformanceStats(),
      debugEnabled: this.debugEnabled,
      breakpointEnabled: this.breakpointEnabled,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }

  /**
   * 获取所有连接的节点
   * @returns 连接的节点列表
   */
  public getConnectedNodes(): Node[] {
    const connectedNodes = new Set<Node>();

    // 添加输入连接的源节点
    for (const connection of this.inputConnections.values()) {
      connectedNodes.add(connection.sourceNode);
    }

    // 添加输出连接的目标节点
    for (const connections of this.outputConnections.values()) {
      for (const connection of connections) {
        connectedNodes.add(connection.targetNode);
      }
    }

    return Array.from(connectedNodes);
  }

  /**
   * 检查是否与指定节点连接
   * @param node 目标节点
   * @returns 是否连接
   */
  public isConnectedTo(node: Node): boolean {
    // 检查输入连接
    for (const connection of this.inputConnections.values()) {
      if (connection.sourceNode.id === node.id) {
        return true;
      }
    }

    // 检查输出连接
    for (const connections of this.outputConnections.values()) {
      for (const connection of connections) {
        if (connection.targetNode.id === node.id) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * 获取节点的依赖节点（输入连接的源节点）
   * @returns 依赖节点列表
   */
  public getDependencies(): Node[] {
    const dependencies: Node[] = [];

    for (const connection of this.inputConnections.values()) {
      dependencies.push(connection.sourceNode);
    }

    return dependencies;
  }

  /**
   * 获取节点的依赖者（输出连接的目标节点）
   * @returns 依赖者节点列表
   */
  public getDependents(): Node[] {
    const dependents: Node[] = [];

    for (const connections of this.outputConnections.values()) {
      for (const connection of connections) {
        dependents.push(connection.targetNode);
      }
    }

    return dependents;
  }

  /**
   * 检查节点是否可以执行
   * @returns 是否可以执行
   */
  public canExecute(): boolean {
    // 检查执行状态
    if (this.executionState === NodeExecutionState.EXECUTING) {
      return false;
    }

    // 验证节点配置
    const validation = this.validate();
    if (!validation.valid) {
      return false;
    }

    // 检查依赖节点是否已执行
    for (const dependency of this.getDependencies()) {
      if (!dependency.isExecuted() && dependency.nodeType !== NodeType.EVENT) {
        return false;
      }
    }

    return true;
  }

  /**
   * 获取创建时间
   * @returns 创建时间
   */
  public getCreatedAt(): Date {
    return this.createdAt;
  }

  /**
   * 获取最后修改时间
   * @returns 最后修改时间
   */
  public getUpdatedAt(): Date {
    return this.updatedAt;
  }

  /**
   * 标记节点已修改
   */
  protected markAsUpdated(): void {
    this.updatedAt = new Date();
    this.emit('updated');
  }
}
