/**
 * 场景理解节点
 */
import { AsyncNode } from './AsyncNode';
import { NodeCategory, SocketType } from './Node';

export class NLPSceneUnderstandingNode extends AsyncNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'nlp/scene/understand',
      category: NodeCategory.AI,
      name: '场景理解',
      description: '分析自然语言文本并提取场景信息'
    });

    this.setupSockets();
  }

  private setupSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      description: '输入文本',
      optional: false
    });

    this.addInput({
      name: 'enable_ai',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '启用AI增强',
      defaultValue: false,
      optional: true
    });

    this.addInput({
      name: 'ai_services',
      type: SocketType.DATA,
      dataType: 'array',
      description: 'AI服务列表',
      optional: true
    });

    // 输出插槽
    this.addOutput({
      name: 'entities',
      type: SocketType.DATA,
      dataType: 'array',
      description: '识别的实体'
    });

    this.addOutput({
      name: 'intent',
      type: SocketType.DATA,
      dataType: 'string',
      description: '意图分类'
    });

    this.addOutput({
      name: 'sentiment',
      type: SocketType.DATA,
      dataType: 'string',
      description: '情感倾向'
    });

    this.addOutput({
      name: 'keywords',
      type: SocketType.DATA,
      dataType: 'array',
      description: '关键词列表'
    });

    this.addOutput({
      name: 'style',
      type: SocketType.DATA,
      dataType: 'string',
      description: '推断风格'
    });

    this.addOutput({
      name: 'complexity',
      type: SocketType.DATA,
      dataType: 'number',
      description: '场景复杂度'
    });

    this.addOutput({
      name: 'spatial_relations',
      type: SocketType.DATA,
      dataType: 'array',
      description: '空间关系'
    });

    this.addOutput({
      name: 'temporal_context',
      type: SocketType.DATA,
      dataType: 'object',
      description: '时间上下文'
    });

    this.addOutput({
      name: 'cultural_context',
      type: SocketType.DATA,
      dataType: 'object',
      description: '文化上下文'
    });

    this.addOutput({
      name: 'emotional_tone',
      type: SocketType.DATA,
      dataType: 'object',
      description: '情感色调'
    });

    this.addOutput({
      name: 'understanding',
      type: SocketType.DATA,
      dataType: 'object',
      description: '完整理解结果'
    });

    // 流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      description: '理解成功'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.FLOW,
      description: '理解失败'
    });
  }

  protected async executeAsyncImpl(inputs: Record<string, any>): Promise<any> {
    const text = this.getInputValue('text') as string;
    const enableAI = this.getInputValue('enable_ai') as boolean || false;
    const aiServices = this.getInputValue('ai_services') as string[] || [];

    // 验证输入
    if (!text || text.trim().length === 0) {
      throw new Error('输入文本不能为空');
    }

    const world = this.context.world;
    const nlpGenerator = world.getSystem('NLPSceneGenerator');

    if (!nlpGenerator) {
      throw new Error('自然语言场景生成器未初始化');
    }

    // 执行文本理解
    const understanding = await (nlpGenerator as any).understandText(text);

    // 设置所有输出值
    this.setOutputValue('entities', understanding.entities);
    this.setOutputValue('intent', understanding.intent);
    this.setOutputValue('sentiment', understanding.sentiment);
    this.setOutputValue('keywords', understanding.keywords);
    this.setOutputValue('style', understanding.style);
    this.setOutputValue('complexity', understanding.complexity);
    this.setOutputValue('spatial_relations', understanding.spatialRelations);
    this.setOutputValue('temporal_context', understanding.temporalContext);
    this.setOutputValue('cultural_context', understanding.culturalContext);
    this.setOutputValue('emotional_tone', understanding.emotionalTone);
    this.setOutputValue('understanding', understanding);

    return { success: true, understanding };
  }

  // 节点配置
  getConfiguration(): any {
    return {
      color: '#4CAF50',
      icon: 'brain',
      category: 'AI',
      tags: ['nlp', 'understanding', 'analysis', 'text'],
      examples: [
        {
          name: '基础文本理解',
          description: '分析简单的场景描述',
          inputs: {
            text: '创建一个现代办公室，有桌子和椅子',
            enable_ai: false
          }
        },
        {
          name: 'AI增强理解',
          description: '使用AI服务增强文本理解',
          inputs: {
            text: '创建一个温馨的咖啡厅，有木质桌椅、暖色灯光和绿色植物',
            enable_ai: true,
            ai_services: ['openai_gpt4']
          }
        }
      ]
    };
  }
}
