/**
 * AI服务注册节点
 */
import { Node, NodeCategory, SocketDirection, SocketType, NodeOptions } from './Node';
import { NLPSceneGenerator, AICapability } from '../../ai/NLPSceneGenerator';

export class NLPAIServiceNode extends Node {
  public readonly category: NodeCategory = NodeCategory.AI;

  constructor(options: NodeOptions) {
    super(options);

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '注册AI服务';
    }
    if (!this.metadata.description) {
      this.metadata.description = '注册外部AI服务用于场景生成增强';
    }
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'service_name',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'string',
      description: '服务名称',
      optional: false
    });

    this.addInput({
      name: 'endpoint',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'string',
      description: 'API端点',
      optional: false
    });

    this.addInput({
      name: 'api_key',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'string',
      description: 'API密钥',
      optional: true
    });

    this.addInput({
      name: 'model',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'string',
      description: '模型名称',
      defaultValue: 'default',
      optional: true
    });

    this.addInput({
      name: 'capabilities',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'array',
      description: 'AI能力列表',
      optional: true
    });

    this.addInput({
      name: 'rate_limits',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'object',
      description: '速率限制配置',
      optional: true
    });

    this.addInput({
      name: 'fallback_service',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'string',
      description: '备用服务名称',
      optional: true
    });

    // 输出插槽
    this.addOutput({
      name: 'service_config',
      direction: SocketDirection.OUTPUT,
      type: SocketType.DATA,
      dataType: 'object',
      description: 'AI服务配置'
    });

    this.addOutput({
      name: 'success',
      direction: SocketDirection.OUTPUT,
      type: SocketType.FLOW,
      description: '注册成功'
    });

    this.addOutput({
      name: 'error',
      direction: SocketDirection.OUTPUT,
      type: SocketType.FLOW,
      description: '注册失败'
    });
  }

  protected executeImpl(): any {
    const serviceName = this.getInputValue('service_name') as string;
    const endpoint = this.getInputValue('endpoint') as string;
    const apiKey = this.getInputValue('api_key') as string;
    const model = this.getInputValue('model') as string || 'default';
    const capabilities = this.getInputValue('capabilities') as string[] || ['text_understanding'];
    const rateLimits = this.getInputValue('rate_limits') as any;
    const fallbackService = this.getInputValue('fallback_service') as string;

    // 验证输入
    if (!serviceName || serviceName.trim().length === 0) {
      this.setOutputValue('error', '服务名称不能为空');
      this.triggerFlow('error');
      return { success: false, error: '服务名称不能为空' };
    }

    if (!endpoint || endpoint.trim().length === 0) {
      this.setOutputValue('error', 'API端点不能为空');
      this.triggerFlow('error');
      return { success: false, error: 'API端点不能为空' };
    }

    try {
      const nlpGenerator = this.context.world.getSystem(NLPSceneGenerator);

      if (!nlpGenerator) {
        throw new Error('自然语言场景生成器未初始化');
      }

      // 构建AI服务配置
      const serviceConfig = {
        name: serviceName,
        endpoint,
        apiKey,
        model,
        capabilities: this.parseCapabilities(capabilities),
        rateLimits: rateLimits || {
          requestsPerMinute: 60,
          requestsPerHour: 1000,
          maxConcurrent: 5
        },
        fallbackService
      };

      // 注册AI服务
      nlpGenerator.registerAIService(serviceConfig);

      // 设置输出值
      this.setOutputValue('service_config', serviceConfig);

      this.triggerFlow('success');
      return { success: true, serviceConfig };

    } catch (error: any) {
      console.error('注册AI服务失败:', error);
      this.setOutputValue('error', error.message);
      this.triggerFlow('error');
      return { success: false, error: error.message };
    }
  }

  private parseCapabilities(capabilities: string[]): AICapability[] {
    const validCapabilities = Object.values(AICapability);

    return capabilities
      .filter(cap => validCapabilities.includes(cap as AICapability))
      .map(cap => cap as AICapability);
  }

  // 节点配置
  getConfiguration(): any {
    return {
      color: '#2196F3',
      icon: 'cloud',
      category: 'AI',
      tags: ['nlp', 'ai', 'service', 'register'],
      examples: [
        {
          name: '注册OpenAI服务',
          description: '注册OpenAI GPT服务',
          inputs: {
            service_name: 'openai_gpt4',
            endpoint: 'https://api.openai.com/v1',
            model: 'gpt-4',
            capabilities: ['text_understanding', 'scene_planning'],
            rate_limits: {
              requestsPerMinute: 60,
              requestsPerHour: 1000,
              maxConcurrent: 5
            }
          }
        },
        {
          name: '注册本地NLP服务',
          description: '注册本地NLP服务',
          inputs: {
            service_name: 'local_bert',
            endpoint: 'http://localhost:8080/api',
            model: 'bert-base-chinese',
            capabilities: ['text_understanding'],
            rate_limits: {
              requestsPerMinute: 120,
              requestsPerHour: 5000,
              maxConcurrent: 10
            },
            fallback_service: 'openai_gpt4'
          }
        }
      ]
    };
  }
}
