/**
 * NLP性能监控节点
 */
import { Node, NodeCategory, SocketType } from './Node';

export class NLPPerformanceNode extends Node {
  constructor(options: any) {
    super({
      ...options,
      type: 'nlp/performance/monitor',
      category: NodeCategory.AI,
      name: 'NLP性能监控',
      description: '监控NLP场景生成器的性能指标'
    });

    this.setupSockets();
  }

  private setupSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'action',
      type: SocketType.DATA,
      dataType: 'string',
      description: '操作类型',
      defaultValue: 'get_metrics',
      optional: true
    });

    this.addInput({
      name: 'cache_limit',
      type: SocketType.DATA,
      dataType: 'number',
      description: '缓存大小限制',
      optional: true
    });

    // 输出插槽
    this.addOutput({
      name: 'total_generations',
      type: SocketType.DATA,
      dataType: 'number',
      description: '总生成次数'
    });

    this.addOutput({
      name: 'average_time',
      type: SocketType.DATA,
      dataType: 'number',
      description: '平均生成时间(ms)'
    });

    this.addOutput({
      name: 'cache_hit_rate',
      type: SocketType.DATA,
      dataType: 'number',
      description: '缓存命中率'
    });

    this.addOutput({
      name: 'error_rate',
      type: SocketType.DATA,
      dataType: 'number',
      description: '错误率'
    });

    this.addOutput({
      name: 'custom_styles',
      type: SocketType.DATA,
      dataType: 'array',
      description: '已注册自定义风格'
    });

    this.addOutput({
      name: 'custom_objects',
      type: SocketType.DATA,
      dataType: 'array',
      description: '已注册自定义对象'
    });

    this.addOutput({
      name: 'ai_services',
      type: SocketType.DATA,
      dataType: 'array',
      description: '已注册AI服务'
    });

    this.addOutput({
      name: 'metrics',
      type: SocketType.DATA,
      dataType: 'object',
      description: '完整性能指标'
    });

    // 流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      description: '操作成功'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.FLOW,
      description: '操作失败'
    });
  }

  execute(inputs: Record<string, any>): any {
    const action = inputs.action as string || 'get_metrics';
    const cacheLimit = inputs.cache_limit as number;

    try {
      const world = this.context.getWorld();
      const nlpGenerator = world.getSystem('NLPSceneGenerator');

      if (!nlpGenerator) {
        throw new Error('自然语言场景生成器未初始化');
      }

      switch (action) {
        case 'get_metrics':
          return this.getMetrics(nlpGenerator);
        
        case 'clear_cache':
          return this.clearCache(nlpGenerator);
        
        case 'set_cache_limit':
          return this.setCacheLimit(nlpGenerator, cacheLimit);
        
        default:
          return this.getMetrics(nlpGenerator);
      }

    } catch (error: any) {
      console.error('性能监控操作失败:', error);
      this.setOutputValue('error', error.message);
      this.triggerFlow('error');
      return { success: false, error: error.message };
    }
  }

  private getMetrics(nlpGenerator: any): any {
    try {
      // 获取性能指标
      const metrics = nlpGenerator.getPerformanceMetrics();
      
      // 获取注册信息
      const customStyles = nlpGenerator.getCustomStyles();
      const customObjects = nlpGenerator.getCustomObjects();
      const aiServices = nlpGenerator.getAIServices();

      // 设置输出值
      this.setOutputValue('total_generations', metrics.totalGenerations);
      this.setOutputValue('average_time', metrics.averageGenerationTime);
      this.setOutputValue('cache_hit_rate', metrics.cacheHitRate);
      this.setOutputValue('error_rate', metrics.errorRate);
      this.setOutputValue('custom_styles', customStyles);
      this.setOutputValue('custom_objects', customObjects);
      this.setOutputValue('ai_services', aiServices);

      const fullMetrics = {
        performance: metrics,
        registrations: {
          customStyles,
          customObjects,
          aiServices
        },
        timestamp: Date.now()
      };

      this.setOutputValue('metrics', fullMetrics);

      this.triggerFlow('success');
      return { success: true, metrics: fullMetrics };

    } catch (error: any) {
      throw new Error(`获取性能指标失败: ${error.message}`);
    }
  }

  private clearCache(nlpGenerator: any): any {
    try {
      nlpGenerator.clearCache();
      
      this.setOutputValue('metrics', {
        action: 'cache_cleared',
        timestamp: Date.now()
      });

      this.triggerFlow('success');
      return { success: true, action: 'cache_cleared' };

    } catch (error: any) {
      throw new Error(`清除缓存失败: ${error.message}`);
    }
  }

  private setCacheLimit(nlpGenerator: any, limit: number): any {
    try {
      if (!limit || limit <= 0) {
        throw new Error('缓存限制必须是正数');
      }

      nlpGenerator.setCacheLimit(limit);
      
      this.setOutputValue('metrics', {
        action: 'cache_limit_set',
        limit,
        timestamp: Date.now()
      });

      this.triggerFlow('success');
      return { success: true, action: 'cache_limit_set', limit };

    } catch (error: any) {
      throw new Error(`设置缓存限制失败: ${error.message}`);
    }
  }

  // 节点配置
  getConfiguration(): any {
    return {
      color: '#FF9800',
      icon: 'chart-line',
      category: 'AI',
      tags: ['nlp', 'performance', 'monitoring', 'metrics'],
      examples: [
        {
          name: '获取性能指标',
          description: '获取当前性能指标',
          inputs: {
            action: 'get_metrics'
          }
        },
        {
          name: '清除缓存',
          description: '清除所有缓存',
          inputs: {
            action: 'clear_cache'
          }
        },
        {
          name: '设置缓存限制',
          description: '设置缓存大小限制',
          inputs: {
            action: 'set_cache_limit',
            cache_limit: 100
          }
        }
      ]
    };
  }
}
